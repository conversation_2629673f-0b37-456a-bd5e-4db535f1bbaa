"""
配置文件 - MinERU Content List 处理器
"""

import os
from pathlib import Path

# LLM配置
# LLM_API_KEY = "sk-6119bb6e5a95440598ef80c25a40dc8e"  # API密钥
# LLM_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"  # API基础URL
# LLM_MODEL = "qwen-plus"  # 模型名称，如qwen-plus、glm-4、gpt-4等
LLM_API_KEY = "68bdb68643b04400b831b949f1294c7f.GxUeBCwzI1Q9y1gb"  # API密钥
LLM_BASE_URL = "https://open.bigmodel.cn/api/paas/v4"  # API基础URL
LLM_MODEL = "glm-4.5"  # 模型名称，如qwen-plus、glm-4、gpt-4等
LLM_TEMPERATURE = 0.1
LLM_MAX_TOKENS = 60000

LLM_TIMEOUT = 6000  # 秒

# Think模式配置
THINK_MODE = False  # 是否启用Think模式

# 文件路径配置
PROJECT_ROOT = Path(__file__).parent
LIST_DIR = PROJECT_ROOT / "list"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "tmp"
PROMPTS_DIR = PROJECT_ROOT / "prompts"

# 确保目录存在
OUTPUT_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
PROMPTS_DIR.mkdir(exist_ok=True)

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 1  # 秒

# 处理配置
DEFAULT_CONTEXT_LINES = 3  # 提取表格行数用于判断
MAX_TEXT_LENGTH_FOR_JOIN = 200  # 文本拼接判断的最大长度

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = PROJECT_ROOT / "logs" / "processor.log"

# 确保日志目录存在
LOG_FILE.parent.mkdir(exist_ok=True)

# Markdown配置
MARKDOWN_TABLE_ALIGN = "left"  # left, center, right
MARKDOWN_LINE_BREAK = "\n\n"

# 提示词配置
PROMPTS_FILE = PROMPTS_DIR / "llm_prompts.md"

# 表格合并模式配置
TABLE_MERGE_MODE = os.getenv("TABLE_MERGE_MODE", "rule_only")  # 表格合并模式
# 支持的模式：
# - "llm_fix": LLM直接修复模式（默认）
# - "llm_judge": LLM判断+规则合并模式
# - "rule_only": 纯规则合并模式（不调用LLM）

# 调试配置
DEBUG_MODE = False
SAVE_INTERMEDIATE_FILES = True  # 是否保存中间处理文件
