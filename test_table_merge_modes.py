#!/usr/bin/env python3
"""
测试表格合并模式功能
验证三种模式都能正常工作：llm_fix、llm_judge、rule_only
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from processors.content_processor import ContentProcessor
from utils.logger import setup_logger

# 设置日志
logger = setup_logger("test_table_merge_modes")


def create_test_tables():
    """创建测试表格数据"""
    table1 = {
        "type": "table",
        "page_idx": 0,
        "table_body": """<table>
<tr><th>姓名</th><th>年龄</th><th>城市</th></tr>
<tr><td>张三</td><td>25</td><td></td></tr>
</table>""",
        "table_caption": [],
        "table_footnote": [],
        "img_path": ""
    }
    
    table2 = {
        "type": "table", 
        "page_idx": 1,
        "table_body": """<table>
<tr><td></td><td></td><td>北京</td></tr>
<tr><td>李四</td><td>30</td><td>上海</td></tr>
</table>""",
        "table_caption": [],  # 空标题，符合续表条件
        "table_footnote": [],
        "img_path": ""
    }
    
    return table1, table2


def test_mode(mode_name: str, table_merge_mode: str):
    """测试指定模式"""
    print(f"\n{'='*50}")
    print(f"测试模式: {mode_name} ({table_merge_mode})")
    print(f"{'='*50}")
    
    try:
        # 创建测试数据
        table1, table2 = create_test_tables()
        
        # 创建处理器
        processor = ContentProcessor(table_merge_mode=table_merge_mode)
        
        # 测试续表合并
        result = processor._handle_table_continuation(table1, table2)
        
        if result:
            print("✅ 合并成功")
            print(f"合并后的表格体长度: {len(result.get('table_body', ''))}")
            print(f"页面索引: {result.get('page_idx')}")
            
            # 显示合并结果的前200个字符
            body = result.get('table_body', '')
            if len(body) > 200:
                print(f"表格体预览: {body[:200]}...")
            else:
                print(f"表格体: {body}")
                
            return True
        else:
            print("❌ 合并失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_multiple_tables(mode_name: str, table_merge_mode: str):
    """测试多表格合并"""
    print(f"\n{'='*50}")
    print(f"测试多表格合并: {mode_name} ({table_merge_mode})")
    print(f"{'='*50}")
    
    try:
        # 创建三个测试表格
        table1, table2 = create_test_tables()
        
        # 创建第三个表格
        table3 = {
            "type": "table",
            "page_idx": 2,
            "table_body": """<table>
<tr><td>王五</td><td>28</td><td>广州</td></tr>
</table>""",
            "table_caption": [],
            "table_footnote": [],
            "img_path": ""
        }
        
        # 创建处理器
        processor = ContentProcessor(table_merge_mode=table_merge_mode)
        
        # 测试多表格合并
        result = processor.handle_multiple_table_continuation([table1, table2, table3])
        
        if result:
            print("✅ 多表格合并成功")
            print(f"合并后的表格体长度: {len(result.get('table_body', ''))}")
            print(f"页面索引: {result.get('page_idx')}")
            return True
        else:
            print("❌ 多表格合并失败")
            return False
            
    except Exception as e:
        print(f"❌ 多表格测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print(f"\n{'='*50}")
    print("测试向后兼容性")
    print(f"{'='*50}")
    
    try:
        # 测试原有的use_llm_fix参数
        table1, table2 = create_test_tables()
        
        # 测试use_llm_fix=True
        processor_true = ContentProcessor(use_llm_fix=True)
        result_true = processor_true._handle_table_continuation(table1, table2)
        
        # 测试use_llm_fix=False  
        processor_false = ContentProcessor(use_llm_fix=False)
        result_false = processor_false._handle_table_continuation(table1, table2)
        
        if result_true and result_false:
            print("✅ 向后兼容性测试通过")
            print(f"use_llm_fix=True 模式: {processor_true.table_merge_mode}")
            print(f"use_llm_fix=False 模式: {processor_false.table_merge_mode}")
            return True
        else:
            print("❌ 向后兼容性测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试表格合并模式功能")
    
    # 测试结果统计
    results = []
    
    # 测试三种模式
    modes = [
        ("LLM直接修复模式", "llm_fix"),
        ("LLM判断+规则合并模式", "llm_judge"), 
        ("纯规则合并模式", "rule_only")
    ]
    
    for mode_name, table_merge_mode in modes:
        # 测试单表格合并
        success1 = test_mode(mode_name, table_merge_mode)
        results.append((f"{mode_name} - 单表格", success1))
        
        # 测试多表格合并
        success2 = test_multiple_tables(mode_name, table_merge_mode)
        results.append((f"{mode_name} - 多表格", success2))
    
    # 测试向后兼容性
    success3 = test_backward_compatibility()
    results.append(("向后兼容性", success3))
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print(f"{'='*50}")
    
    success_count = 0
    total_count = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("⚠️  部分测试失败，请检查日志")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
